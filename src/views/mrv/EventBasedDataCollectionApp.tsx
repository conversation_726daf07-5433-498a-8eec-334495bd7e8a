import {LocalizationProvider} from '@mui/x-date-pickers-pro';
import {AdapterDateFns} from '@mui/x-date-pickers-pro/AdapterDateFns';
import React from 'react';
import {Route} from 'react-router-dom';

import {Box} from '@regrow-internal/design-system';

import {useDateFnsLocale} from '_translations/utils';

import {TOP_BAR_HEIGHT} from 'containers/mrv/components/navigation-top-bar/constants';
import {MRV_STAGE} from 'containers/mrv/routes';

import {PhaseNavigation} from './project/phases/phase-navigation/PhaseNavigation';
import {PhaseContextProvider} from './project/phases/PhaseContext';
import {FieldEventsStage} from './project/phases/stages/field-events/FieldEventsStages';
import {FieldNotificationContextProvider} from './project/phases/stages/FieldNotificationContext';
import {SelectedFieldContextProvider} from './project/phases/stages/SelectedFieldContext';
import {StageContextProvider} from './project/phases/stages/StageContext';

export const EventBasedDataCollectionApp = () => {
  const locale = useDateFnsLocale();

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={locale}>
      <PhaseContextProvider>
        <Box height="100%">
          <PhaseNavigation />
          <Box p={4} height={`calc(100% - ${TOP_BAR_HEIGHT}px)`} overflow="auto">
            <SelectedFieldContextProvider>
              <FieldNotificationContextProvider>
                <Route path={MRV_STAGE}>
                  <StageContextProvider>
                    <FieldEventsStage />
                  </StageContextProvider>
                </Route>
              </FieldNotificationContextProvider>
            </SelectedFieldContextProvider>
          </Box>
        </Box>
      </PhaseContextProvider>
    </LocalizationProvider>
  );
};
