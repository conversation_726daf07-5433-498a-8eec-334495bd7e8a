import React, {createContext, useCallback, useContext, useEffect, useMemo} from 'react';
import type {FC} from 'react';

import {FieldNotificationLevel} from './FieldNotificationContext';
import {useSelectedFieldContext} from './SelectedFieldContext';

export enum FieldNotificationLevel {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
}
export type FieldNotification = {
  [FieldNotificationLevel.SUCCESS]?: Array<string>;
  [FieldNotificationLevel.WARNING]?: Array<string>;
  [FieldNotificationLevel.ERROR]?: Array<string>;
};
export type FieldNotifications = {
  [fieldId: string]: FieldNotification;
};
export type FieldNotificationContextType = {
  fieldNotifications: FieldNotifications;
  setNotificationForField: (
    fieldId: string,
    level: FieldNotificationLevel,
    message: string
  ) => void;
  setNotificationForFields: (
    fieldIds: Array<string>,
    level: FieldNotificationLevel,
    message: string
  ) => void;
  clearNotificationForField: (fieldId: string, level?: FieldNotificationLevel) => void;
};

const initialState = {
  fieldNotifications: {},
  setNotificationForField: () => undefined,
  setNotificationForFields: () => undefined,
  clearNotificationForField: () => undefined,
};

const FieldNotificationContext = createContext<FieldNotificationContextType>(initialState);

export const FieldNotificationContextProvider: FC = ({children}) => {
  const {selectedField} = useSelectedFieldContext();
  const [fieldNotifications, setNotificationForField] = React.useState<FieldNotifications>(
    initialState.fieldNotifications
  );

  const handleSetFieldNotifications = useCallback(
    (fieldId: string, level: FieldNotificationLevel, message: string) => {
      setNotificationForField(previousState => {
        const notificationsForField = previousState[fieldId];
        const notificationsForLevel = notificationsForField ? notificationsForField[level] : null;

        return {
          ...previousState,
          [fieldId]: {
            ...notificationsForField,
            [level]:
              notificationsForField && notificationsForLevel
                ? [...notificationsForLevel, message]
                : [message],
          },
        };
      });
    },
    []
  );

  const setNotificationForFields = useCallback(
    (fieldIds: Array<string>, level: FieldNotificationLevel, message: string) => {
      setNotificationForField(previousState => {
        const newState = {...previousState};

        fieldIds.forEach(fieldId => {
          const notificationsForField = previousState[fieldId];
          const notificationsForLevel = notificationsForField ? notificationsForField[level] : null;

          newState[fieldId] = {
            ...notificationsForField,
            [level]:
              notificationsForField && notificationsForLevel
                ? [...notificationsForLevel, message]
                : [message],
          };
        });

        return newState;
      });
    },
    []
  );

  const clearNotificationForField = useCallback(
    (fieldId: string, level?: FieldNotificationLevel) => {
      setNotificationForField(previousState => {
        const previousFieldState = previousState[fieldId];

        if (!previousFieldState) {
          return previousState;
        }

        if (level) {
          return {
            ...previousState,
            [fieldId]: {
              ...previousFieldState,
              [level]: undefined,
            },
          };
        } else {
          delete previousState[fieldId];
          return previousState;
        }
      });
    },
    []
  );

  useEffect(() => {
    const selectedFieldId = selectedField?.id;
    if (selectedFieldId && fieldNotifications[selectedFieldId]?.[FieldNotificationLevel.SUCCESS]) {
      // Clear success notifications on field view
      clearNotificationForField(selectedFieldId, FieldNotificationLevel.SUCCESS);
    }
  }, [selectedField, fieldNotifications, clearNotificationForField]);

  const selectedFieldContext = useMemo(
    () => ({
      fieldNotifications,
      setNotificationForField: handleSetFieldNotifications,
      setNotificationForFields,
      clearNotificationForField,
    }),
    [
      clearNotificationForField,
      fieldNotifications,
      handleSetFieldNotifications,
      setNotificationForFields,
    ]
  );

  return (
    <FieldNotificationContext.Provider value={selectedFieldContext}>
      {children}
    </FieldNotificationContext.Provider>
  );
};

export const useFieldNotificationContext = () => {
  return useContext<FieldNotificationContextType>(FieldNotificationContext);
};
