import {act, waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import type {ReactNode} from 'react';
import React from 'react';

import {SimpleProviders} from '_common/test_utils/renderWithProviders';

import {
  FieldNotificationContextProvider,
  FieldNotificationLevel,
  useFieldNotificationContext,
} from 'views/mrv/project/phases/stages/FieldNotificationContext';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');

const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
  typeof useSelectedFieldContext
>;

const renderTestComponent = (children: ReactNode) => {
  return (
    <SimpleProviders>
      <FieldNotificationContextProvider>{children}</FieldNotificationContextProvider>
    </SimpleProviders>
  );
};

const renderHookWithProvider = () => {
  return renderHook(() => useFieldNotificationContext(), {
    wrapper: ({children}) => renderTestComponent(children),
  });
};

describe('FieldNotificationContext', () => {
  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      selectedField: null,
      selectedFarm: null,
      farms: [],
      setSelectedField: jest.fn(),
      selectedFieldEvents: {field: null, events: null},
      setSelectedFieldEvents: jest.fn(),
      clearSelectedFieldEvents: jest.fn(),
      isLoading: false,
      error: null,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should provide initial empty state', () => {
    const {result} = renderHookWithProvider();

    expect(result.current.fieldNotifications).toEqual({});
    expect(result.current.setNotificationForField).toBeInstanceOf(Function);
    expect(result.current.setNotificationForFields).toBeInstanceOf(Function);
    expect(result.current.clearNotificationForField).toBeInstanceOf(Function);
  });

  it('should set notification for a single field', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'Test success message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [FieldNotificationLevel.SUCCESS]: ['Test success message'],
      },
    });
  });

  it('should set notifications for multiple fields', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForFields(
        ['field-1', 'field-2'],
        FieldNotificationLevel.SUCCESS,
        'Bulk success message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [FieldNotificationLevel.SUCCESS]: ['Bulk success message'],
      },
      'field-2': {
        [FieldNotificationLevel.SUCCESS]: ['Bulk success message'],
      },
    });
  });

  it('should append multiple notifications for the same field and level', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'First message'
      );
    });

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'Second message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [FieldNotificationLevel.SUCCESS]: ['First message', 'Second message'],
      },
    });
  });

  it('should handle different notification levels for the same field', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'Success message'
      );
    });

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.ERROR,
        'Error message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [FieldNotificationLevel.SUCCESS]: ['Success message'],
        [FieldNotificationLevel.ERROR]: ['Error message'],
      },
    });
  });

  it('should clear specific notification level for a field', () => {
    const {result} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'Success message'
      );
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.ERROR,
        'Error message'
      );
    });

    // Clear only success notifications
    act(() => {
      result.current.clearNotificationForField('field-1', FieldNotificationLevel.SUCCESS);
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [FieldNotificationLevel.SUCCESS]: undefined,
        [FieldNotificationLevel.ERROR]: ['Error message'],
      },
    });
  });

  it('should clear all notifications for a field when no level specified', () => {
    const {result} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'Success message'
      );
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.ERROR,
        'Error message'
      );
      result.current.setNotificationForField(
        'field-2',
        FieldNotificationLevel.WARNING,
        'Warning message'
      );
    });

    // Clear all notifications for field-1
    act(() => {
      result.current.clearNotificationForField('field-1');
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-2': {
        [FieldNotificationLevel.WARNING]: ['Warning message'],
      },
    });
  });

  it('should automatically clear success notifications when field is selected', async () => {
    const {result, rerender} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.SUCCESS,
        'Success message'
      );
      result.current.setNotificationForField(
        'field-1',
        FieldNotificationLevel.ERROR,
        'Error message'
      );
    });

    // Simulate field selection
    mockUseSelectedFieldContext.mockReturnValue({
      selectedField: {id: 'field-1', name: 'Test Field'},
      selectedFarm: null,
      farms: [],
      setSelectedField: jest.fn(),
      selectedFieldEvents: {field: null, events: null},
      setSelectedFieldEvents: jest.fn(),
      clearSelectedFieldEvents: jest.fn(),
      isLoading: false,
      error: null,
    });

    rerender();

    await waitFor(() => {
      expect(result.current.fieldNotifications).toEqual({
        'field-1': {
          [FieldNotificationLevel.SUCCESS]: undefined,
          [FieldNotificationLevel.ERROR]: ['Error message'],
        },
      });
    });
  });

  it('should handle clearing notifications for non-existent field gracefully', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.clearNotificationForField('non-existent-field');
    });

    expect(result.current.fieldNotifications).toEqual({});
  });
});
