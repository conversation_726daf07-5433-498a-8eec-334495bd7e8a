import {getFragmentData} from '__generated__/gql/fragment-masking';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
} from '__generated__/gql/graphql';
import type {GetPhaseFieldCultivationCycleEventsQuery} from '__generated__/gql/graphql';

import type {FieldEventCount} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';

/**
 * Processes field event data from all phases and stages to count event types
 * @param phases - a list of MRV phases with stage and event data
 * @param fieldId - the ID of the field being processed
 * @returns FieldEventCount object with fieldId, fieldName, and eventTypeCounts
 */
export const initialFieldCountState: FieldEventCount = {
  field: null,
  eventTypeCounts: null,
};

export const getFieldEventCount = (
  phases:
    | NonNullable<
        NonNullable<GetPhaseFieldCultivationCycleEventsQuery['mrv']['project']>['program']
      >['phases']
    | undefined
): FieldEventCount => {
  const result: FieldEventCount = {...initialFieldCountState};
  if (!phases?.length) return result;

  const eventTypeCounts: Record<string, number> = {};

  for (const phase of phases) {
    const stages = phase?.stages || [];

    for (const stage of stages) {
      const fieldData = stage?.field;
      if (!fieldData) continue;

      const fieldWithEvents = getFragmentData(FieldWithEventsFragmentDoc, fieldData);

      if (!result.field && fieldWithEvents?.id) {
        result.field = {
          id: String(fieldWithEvents.id),
          name: fieldData.name,
        };
      }

      if (!fieldWithEvents?.cultivation_cycles) continue;

      for (const cycle of fieldWithEvents.cultivation_cycles) {
        if (!cycle?.events) continue;

        for (const event of cycle.events) {
          const eventData = getFragmentData(FieldEventAttributesFragmentDoc, event);
          const eventType = eventData?.type;

          if (eventType) {
            eventTypeCounts[eventType] = (eventTypeCounts[eventType] || 0) + 1;
          }
        }
      }
    }
  }

  if (Object.keys(eventTypeCounts).length > 0) {
    result.eventTypeCounts = eventTypeCounts;
  }

  return result;
};
