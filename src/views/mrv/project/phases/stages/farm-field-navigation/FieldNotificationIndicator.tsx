import React, {useMemo} from 'react';

import {Typography, type SvgIconProps} from '@regrow-internal/design-system';

import {
  FieldNotificationLevel,
  type FieldNotification,
} from 'views/mrv/project/phases/stages/FieldNotificationContext';

const notificationColourLookup = {
  [FieldNotificationLevel.SUCCESS]: 'semanticPalette.highlight.success',
  [FieldNotificationLevel.WARNING]: 'semanticPalette.highlight.warning',
  [FieldNotificationLevel.ERROR]: 'semanticPalette.highlight.error',
};

export const FieldNotificationIndicator = ({
  notifications,
}: {
  notifications: FieldNotification | null;
}) => {
  const errorTypeToShow = useMemo(() => {
    if (!notifications) return null;
    if (notifications[FieldNotificationLevel.ERROR]?.length) {
      return FieldNotificationLevel.ERROR;
    }
    if (notifications[FieldNotificationLevel.WARNING]?.length) {
      return FieldNotificationLevel.WARNING;
    }
    if (notifications[FieldNotificationLevel.SUCCESS]?.length) {
      return FieldNotificationLevel.SUCCESS;
    }
    return null;
  }, [notifications]);

  if (!errorTypeToShow) return null;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const colour = notificationColourLookup[errorTypeToShow] as SvgIconProps['color'];

  return (
    <Typography fontSize="18px" color={colour}>
      &#9679;
    </Typography>
  );
};
