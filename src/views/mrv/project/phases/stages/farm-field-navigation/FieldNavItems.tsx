import React, {useEffect, useRef, useState} from 'react';

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  SvgIcon,
  Typography,
  TypographyOverflow,
  useTheme,
} from '@regrow-internal/design-system';

import {isFalsy} from '_common/utils/typeGuards';

import type {FarmFieldNavProps} from './FarmFieldNavigation';
import type {FarmWithSearchFields, FieldNavItemType} from './FarmNavItems';
import {FieldNotificationIndicator} from './FieldNotificationIndicator';

const FieldNavItems = ({
  farm,
  onFieldClick,
  selectedFieldId,
  fieldCompletionLookup,
}: FarmFieldNavProps & {farm: FarmWithSearchFields}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(true);

  return (
    <Box width="100%" mb={0}>
      <Accordion
        size="small"
        expanded={expanded}
        onChange={() => {
          setExpanded(!expanded);
        }}
        sx={{backgroundColor: theme.palette.semanticPalette.surface.secondary}}
      >
        <AccordionSummary
          id={`farm-header-${farm.name}`}
          aria-controls={`farm-fields-${farm.name}`}
        >
          <Typography variant="h6" component="span">
            {farm.name}
          </Typography>
        </AccordionSummary>
        <AccordionDetails id={`farm-fields-${farm.name}`}>
          <Box display="flex" flexDirection="column" gap={1}>
            {farm.fields?.map(field => (
              <MemoizedFieldNavItem
                key={field.id}
                field={field}
                onFieldClick={onFieldClick}
                selectedFieldId={selectedFieldId}
                isFieldComplete={fieldCompletionLookup[field.id]}
              />
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export const MemoizedFieldNavItems = React.memo(FieldNavItems);

const FieldNavItem = ({
  field,
  selectedFieldId,
  onFieldClick,
  isFieldComplete = false,
}: Omit<FarmFieldNavProps, 'fieldCompletionLookup'> & {
  field: FieldNavItemType;
  isFieldComplete?: boolean;
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const theme = useTheme();
  const [tooltipIsOpen, setToolTipIsOpen] = useState(false);
  const isActiveField = selectedFieldId === field.id;

  // Only scroll into view if the field is active, keeps in sync with prev/next buttons.
  useEffect(() => {
    if (buttonRef.current && isActiveField) {
      buttonRef.current.scrollIntoView({behavior: 'smooth', block: 'nearest'});
    }
  }, [isActiveField]);

  // Close the tooltip if it is open and the field scrolls out of view.
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (isFalsy(entry?.isIntersecting)) {
          setToolTipIsOpen(false);
        }
      },
      {
        root: document.querySelector('#field-sidebar-wrapper'),
        rootMargin: '0px',
        threshold: 1.0,
      }
    );

    if (buttonRef.current) {
      observer.observe(buttonRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <Button
      key={field.id}
      ref={buttonRef}
      variant={isActiveField ? 'contained' : 'text'}
      color="secondary"
      fullWidth
      aria-current={isActiveField}
      sx={{
        backgroundColor: isActiveField ? theme.palette.semanticPalette.stroke.main : 'transparent',
        fontSize: theme.typography.fontSize,
        justifyContent: 'space-between',

        '&.MuiButtonBase-root': {p: 1.5},
        textAlign: 'left',
      }}
      onClick={() => {
        onFieldClick(field.id);
      }}
    >
      <TypographyOverflow
        clampLines={1}
        fontWeight={
          isActiveField ? theme.typography.fontWeightBold : theme.typography.fontWeightRegular
        }
        TooltipProps={{
          onOpen: () => {
            setToolTipIsOpen(true);
          },
          open: tooltipIsOpen,
          onClose: () => {
            setToolTipIsOpen(false);
          },
        }}
      >
        {field.name}
      </TypographyOverflow>
      {isFieldComplete ? (
        <SvgIcon
          color="primary"
          type="check-mark"
          fontSize="body1"
          aria-label={`${field.name} is complete`}
        />
      ) : (
        <FieldNotificationIndicator notifications={field.notifications} />
      )}
    </Button>
  );
};

const MemoizedFieldNavItem = React.memo(FieldNavItem);
